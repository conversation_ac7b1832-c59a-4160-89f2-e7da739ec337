#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地址测试工具
单独测试地址有效性和获取交易情况

测试说明：
- 主要用于测试USDT交易查询功能
- USDT合约地址：TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t (TRC20)
- USDT合约地址：0xdAC17F958D2ee523a2206206994597C13D831ec7 (ERC20)

测试用例：
1. 测试地址：TFY5G28sBHfLNvnQ4vRTurxD5JXG8F9C7J
   - 交易时间：2024-07-28 09:41
   - 转账金额：2,335 USDT
   - 接收地址：TU7GSV8V8dAdT3xyf2Ej4aCxFTvicLES4z
   - 交易哈希：ff8f2a510dcb053e60f53dc64d607e6ecc4a1ad4d360c209e23aa5ecde6f607a

2. 测试地址：TLW4uorp3nDoiPpttvr2Ye3SnWwrWRbnae
   - 用于基础地址验证测试

使用方法：
- 选择选项4 "查询USDT交易（推荐）" 来专门查询USDT交易记录
- 系统会自动识别TRC20/ERC20地址类型并调用相应的API
- 查询结果包含交易方向、金额统计等详细信息
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from modules.address_validator import AddressValidator
from modules.address_collector import AddressCollector
from modules.data_reader import ExcelDataReader
from datetime import datetime, timedelta
import json

class AddressTestTools:
    """地址测试工具类"""
    
    def __init__(self):
        """初始化测试工具"""
        self.validator = AddressValidator()
        self.reader = ExcelDataReader(".")
        self.collector = AddressCollector(self.reader)
        
    def test_single_address(self, address: str) -> dict:
        """
        测试单个地址的有效性
        
        Args:
            address: 要测试的地址
            
        Returns:
            测试结果字典
        """
        print(f"\n{'='*60}")
        print(f"测试地址: {address}")
        print(f"{'='*60}")
        
        # 验证地址
        result = self.validator.validate_address(address)
        
        # 打印结果
        print(f"地址类型: {result['address_type']}")
        print(f"格式有效: {result['format_valid']}")
        print(f"链上存在: {result['exists_on_chain']}")
        print(f"应忽略: {result['should_ignore']}")
        
        if result['chain_info']:
            print(f"链上信息: {json.dumps(result['chain_info'], indent=2, ensure_ascii=False)}")
        
        if result['errors']:
            print(f"错误信息: {'; '.join(result['errors'])}")
        
        return result
    
    def test_multiple_addresses(self, addresses: list) -> dict:
        """
        批量测试多个地址
        
        Args:
            addresses: 地址列表
            
        Returns:
            批量测试结果
        """
        print(f"\n{'='*60}")
        print(f"批量测试 {len(addresses)} 个地址")
        print(f"{'='*60}")
        
        results = self.validator.validate_address_list(addresses)
        
        # 打印汇总结果
        print(f"\n📊 测试结果汇总:")
        print(f"   总地址数: {results['total_addresses']}")
        print(f"   有效地址: {len(results['valid_addresses'])}")
        print(f"   无效地址: {len(results['invalid_addresses'])}")
        print(f"   忽略地址: {len(results['ignored_addresses'])}")
        print(f"   TRC20地址: {len(results['trc20_addresses'])}")
        print(f"   ERC20地址: {len(results['erc20_addresses'])}")
        
        # 显示有效地址
        if results['valid_addresses']:
            print(f"\n✅ 有效地址:")
            for addr in results['valid_addresses']:
                print(f"   - {addr}")
        
        # 显示无效地址
        if results['invalid_addresses']:
            print(f"\n❌ 无效地址:")
            for addr in results['invalid_addresses'][:5]:  # 只显示前5个
                print(f"   - {addr}")
            if len(results['invalid_addresses']) > 5:
                print(f"   ... 还有 {len(results['invalid_addresses']) - 5} 个")
        
        return results
    
    def get_address_transactions(self, address: str, timestamp: datetime = None, 
                               time_window_hours: int = 24) -> dict:
        """
        获取地址的交易情况
        
        Args:
            address: 地址
            timestamp: 查询时间点（可选）
            time_window_hours: 时间窗口（小时）
            
        Returns:
            交易信息字典
        """
        print(f"\n{'='*60}")
        print(f"查询地址交易: {address}")
        if timestamp:
            print(f"查询时间: {timestamp}")
            print(f"时间窗口: ±{time_window_hours} 小时")
        print(f"{'='*60}")
        
        # 首先验证地址
        validation = self.validator.validate_address(address)
        if not validation['format_valid']:
            print(f"❌ 地址格式无效: {address}")
            return {'error': '地址格式无效', 'validation': validation}
        
        transactions = []
        
        # 根据地址类型查询交易
        if validation['address_type'] == 'TRC20':
            print("🔍 查询TRC20交易...")
            transactions = self.collector.query_trc20_transaction(address, timestamp)
        elif validation['address_type'] == 'ERC20':
            print("🔍 查询ERC20交易...")
            transactions = self.collector.query_erc20_transaction(address, timestamp)
        else:
            print(f"❌ 不支持的地址类型: {validation['address_type']}")
            return {'error': '不支持的地址类型', 'validation': validation}
        
        # 打印交易结果
        print(f"\n📋 交易查询结果:")
        print(f"   找到交易: {len(transactions)} 条")
        
        if transactions:
            print(f"\n交易详情:")
            for i, tx in enumerate(transactions[:5], 1):  # 只显示前5条
                print(f"   交易 {i}:")
                print(f"     哈希: {tx.get('tx_hash', 'N/A')}")
                print(f"     发送方: {tx.get('from_address', 'N/A')}")
                print(f"     接收方: {tx.get('to_address', 'N/A')}")
                print(f"     金额(原始): {tx.get('amount_raw', tx.get('amount', 'N/A'))}")
                print(f"     金额(USDT): {tx.get('amount_usdt', 'N/A')}")
                print(f"     时间: {tx.get('timestamp', 'N/A')}")
                print(f"     区块: {tx.get('block', 'N/A')}")
                print(f"     代币: {tx.get('token_name', 'N/A')}")
                print()
            
            if len(transactions) > 5:
                print(f"   ... 还有 {len(transactions) - 5} 条交易")
        
        return {
            'address': address,
            'validation': validation,
            'transaction_count': len(transactions),
            'transactions': transactions,
            'query_time': datetime.now(),
            'search_timestamp': timestamp,
            'time_window_hours': time_window_hours
        }
    
    def get_usdt_transactions_only(self, address: str, limit: int = 50) -> dict:
        """
        专门查询USDT交易记录
        
        Args:
            address: 地址
            limit: 返回记录数限制
            
        Returns:
            USDT交易信息字典
        """
        print(f"\n{'='*60}")
        print(f"查询地址USDT交易: {address}")
        print(f"查询限制: {limit} 条记录")
        print(f"{'='*60}")
        
        # 首先验证地址
        validation = self.validator.validate_address(address)
        if not validation['format_valid']:
            print(f"❌ 地址格式无效: {address}")
            return {'error': '地址格式无效', 'validation': validation}
        
        transactions = []
        
        # 根据地址类型查询USDT交易
        if validation['address_type'] == 'TRC20':
            print("🔍 查询TRC20 USDT交易...")
            transactions = self.collector.query_trc20_usdt_transactions(address, limit=limit)
        elif validation['address_type'] == 'ERC20':
            print("🔍 查询ERC20 USDT交易...")
            transactions = self.collector.query_erc20_usdt_transactions(address, limit=limit)
        else:
            print(f"❌ 不支持的地址类型: {validation['address_type']}")
            return {'error': '不支持的地址类型', 'validation': validation}
        
        # 打印交易结果
        print(f"\n📋 USDT交易查询结果:")
        print(f"   找到USDT交易: {len(transactions)} 条")
        
        # 统计信息
        total_in = 0
        total_out = 0
        in_count = 0
        out_count = 0
        
        if transactions:
            print(f"\n💰 USDT交易详情:")
            for i, tx in enumerate(transactions[:10], 1):  # 显示前10条
                from_addr = tx.get('from_address', 'N/A')
                to_addr = tx.get('to_address', 'N/A')
                amount_usdt = tx.get('amount_usdt', 0)
                
                # 判断是转入还是转出
                direction = ""
                if from_addr.lower() == address.lower():
                    direction = "转出 ➡️"
                    total_out += amount_usdt
                    out_count += 1
                elif to_addr.lower() == address.lower():
                    direction = "转入 ⬅️"
                    total_in += amount_usdt
                    in_count += 1
                
                print(f"   交易 {i} ({direction}):")
                print(f"     哈希: {tx.get('tx_hash', 'N/A')}")
                print(f"     发送方: {from_addr}")
                print(f"     接收方: {to_addr}")
                print(f"     金额: {amount_usdt:.6f} USDT")
                print(f"     时间: {tx.get('timestamp', 'N/A')}")
                print(f"     区块: {tx.get('block', 'N/A')}")
                print()
            
            if len(transactions) > 10:
                print(f"   ... 还有 {len(transactions) - 10} 条交易")
            
            # 显示统计信息
            print(f"\n📊 USDT交易统计:")
            print(f"   转入交易: {in_count} 条，总额: {total_in:.6f} USDT")
            print(f"   转出交易: {out_count} 条，总额: {total_out:.6f} USDT")
            print(f"   净余额变化: {total_in - total_out:.6f} USDT")
        
        return {
            'address': address,
            'validation': validation,
            'transaction_count': len(transactions),
            'transactions': transactions,
            'statistics': {
                'total_in': total_in,
                'total_out': total_out,
                'in_count': in_count,
                'out_count': out_count,
                'net_change': total_in - total_out
            },
            'query_time': datetime.now()
        }
    
    def test_known_addresses(self):
        """测试一些已知的地址"""
        print(f"\n{'='*80}")
        print("测试已知地址")
        print(f"{'='*80}")
        
        # 测试地址列表
        test_addresses = [
            # TRC20地址（来自readme.md）
            "TRFkzRWd8tXynZHgen3H7qbpuRSnM2uWew",
            "TUmBkCNdT9YzTpKqwAZwL5s92iTv6Nw8rh",
            
            # ERC20地址（来自readme.md）
            "0x61f73c531c47d345ee642566d9d9fb38db287cdb",
            
            # 小江的UID（应该被忽略）
            "135343225",
            "1023681287", 
            "451144524",
            
            # 小曾的UID（应该保留）
            "96215380",
            
            # 无效地址
            "invalid_address_test",
            
            # 来自数据的地址样本
            "TXVL9P8ZiiABWq6D66HndrtVC2bQegve9q",
            "TM1zzNDZD2DPASbKcgdVoTYhfmYgtfwx9R",
        ]
        
        # 批量测试
        results = self.test_multiple_addresses(test_addresses)
        
        # 对有效地址进行交易查询
        if results['valid_addresses']:
            print(f"\n{'='*60}")
            print("查询有效地址的交易情况")
            print(f"{'='*60}")
            
            for addr in results['valid_addresses'][:3]:  # 只查询前3个
                self.get_address_transactions(addr)
        
        return results

def main():
    """主测试函数"""
    tools = AddressTestTools()
    
    print("MoneyTrace 地址测试工具")
    print("=" * 80)
    
    while True:
        print("\n请选择测试选项:")
        print("1. 测试单个地址")
        print("2. 批量测试地址")
        print("3. 查询地址交易")
        print("4. 查询USDT交易（推荐）")
        print("5. 测试已知地址")
        print("6. 退出")
        
        choice = input("\n请输入选项 (1-6): ").strip()
        
        if choice == '1':
            address = input("请输入要测试的地址: ").strip()
            if address:
                tools.test_single_address(address)
        
        elif choice == '2':
            print("请输入地址列表（每行一个地址，输入空行结束）:")
            addresses = []
            while True:
                addr = input().strip()
                if not addr:
                    break
                addresses.append(addr)
            
            if addresses:
                tools.test_multiple_addresses(addresses)
        
        elif choice == '3':
            address = input("请输入要查询的地址: ").strip()
            if address:
                # 可选：输入查询时间
                time_input = input("请输入查询时间 (YYYY-MM-DD HH:MM:SS，回车跳过): ").strip()
                timestamp = None
                if time_input:
                    try:
                        timestamp = datetime.strptime(time_input, "%Y-%m-%d %H:%M:%S")
                    except ValueError:
                        print("时间格式错误，将查询所有交易")
                
                tools.get_address_transactions(address, timestamp)
        
        elif choice == '4':
            address = input("请输入要查询USDT交易的地址: ").strip()
            if address:
                limit_input = input("请输入查询记录数限制 (默认50): ").strip()
                limit = 50
                if limit_input.isdigit():
                    limit = int(limit_input)
                
                tools.get_usdt_transactions_only(address, limit)
        
        elif choice == '5':
            tools.test_known_addresses()
        
        elif choice == '6':
            print("退出测试工具")
            break
        
        else:
            print("无效选项，请重新选择")

if __name__ == "__main__":
    main()
