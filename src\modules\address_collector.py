#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
收款地址获取模块
通过区块链查询获取小曾真正的收款地址，支持TRC20和ERC20协议
"""

import requests
import time
import pandas as pd
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Set
import logging

try:
    from .data_reader import ExcelDataReader
    from .address_validator import AddressValidator
except ImportError:
    from data_reader import ExcelDataReader
    from address_validator import AddressValidator

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AddressCollector:
    """小曾收款地址收集器"""
    
    def __init__(self, data_reader: ExcelDataReader):
        """
        初始化收集器
        
        Args:
            data_reader: Excel数据读取器
        """
        self.data_reader = data_reader
        self.validator = AddressValidator()
        self.tron_api_base = "https://apilist.tronscanapi.com/api"
        self.eth_api_base = "https://api.etherscan.io/api"
        self.request_delay = 0.3  # API请求间隔
        
    def normalize_datetime(self, dt_value):
        """标准化时间值为datetime对象"""
        if pd.isna(dt_value):
            return None
        
        try:
            if isinstance(dt_value, datetime):
                return dt_value
            elif hasattr(dt_value, 'to_pydatetime'):
                return dt_value.to_pydatetime()
            elif isinstance(dt_value, (int, float)):
                if 1 <= dt_value <= 73050:
                    base_date = datetime(1900, 1, 1)
                    from datetime import timedelta
                    return base_date + timedelta(days=dt_value - 2)
                else:
                    return None
            else:
                return None
        except (ValueError, OverflowError) as e:
            logger.warning(f"时间转换失败: {dt_value}, 错误: {e}")
            return None
    
    def extract_sender_addresses(self) -> List[Dict]:
        """
        从小曾币安充值记录中提取发送地址信息
        
        Returns:
            发送地址信息列表
        """
        # 读取小曾币安充值记录
        xz_deposits = self.data_reader.read_xiaozeng_binance_deposits()
        
        sender_info = []
        for _, record in xz_deposits.iterrows():
            sender_addr = record['发送地址']
            deposit_time = self.normalize_datetime(record['时间'])
            
            if pd.notna(sender_addr):
                # 处理不同类型的地址数据
                if isinstance(sender_addr, str):
                    addr_str = sender_addr.strip()
                elif isinstance(sender_addr, (int, float)):
                    addr_str = str(sender_addr).strip()
                else:
                    continue
                
                if addr_str and addr_str != 'nan':
                    sender_info.append({
                        'sender_address': addr_str,
                        'deposit_time': deposit_time,
                        'usdt_amount': record.get('USDT对应数额', 0),
                        'record_index': record.name
                    })
        
        logger.info(f"提取到 {len(sender_info)} 条发送地址记录")
        return sender_info
    
    def query_trc20_transaction(self, sender_address: str, timestamp: datetime) -> List[Dict]:
        """
        查询TRC20交易，获取接收地址
        
        Args:
            sender_address: 发送地址
            timestamp: 交易时间
            
        Returns:
            交易信息列表
        """
        try:
            # 转换时间戳
            if timestamp:
                start_timestamp = int(timestamp.timestamp() * 1000) - 3600000  # 前1小时
                end_timestamp = int(timestamp.timestamp() * 1000) + 3600000    # 后1小时
            else:
                start_timestamp = 0
                end_timestamp = int(time.time() * 1000)
            
            url = f"{self.tron_api_base}/transaction"
            params = {
                'address': sender_address,
                'start_timestamp': start_timestamp,
                'end_timestamp': end_timestamp,
                'limit': 50
            }
            
            response = requests.get(url, params=params, timeout=15)
            time.sleep(self.request_delay)
            
            if response.status_code == 200:
                data = response.json()
                transactions = []
                
                if 'data' in data:
                    for tx in data['data']:
                        # 查找USDT转账交易
                        if tx.get('contractType') == 31:  # TRC20转账
                            for contract_data in tx.get('contractData', []):
                                if contract_data.get('contract_address') == 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t':  # USDT合约
                                    transactions.append({
                                        'tx_hash': tx.get('hash'),
                                        'from_address': contract_data.get('from_address'),
                                        'to_address': contract_data.get('to_address'),
                                        'amount': contract_data.get('amount', 0),
                                        'timestamp': tx.get('timestamp'),
                                        'block': tx.get('block')
                                    })
                
                return transactions
            else:
                logger.warning(f"TRC20交易查询失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"TRC20交易查询异常 {sender_address}: {e}")
            return []
    
    def query_erc20_transaction(self, sender_address: str, timestamp: datetime) -> List[Dict]:
        """
        查询ERC20交易，获取接收地址

        Args:
            sender_address: 发送地址
            timestamp: 交易时间

        Returns:
            交易信息列表
        """
        try:
            # 免费的Etherscan API（无需API Key，但有频率限制）
            url = self.eth_api_base
            params = {
                'module': 'account',
                'action': 'tokentx',
                'address': sender_address,
                'contractaddress': '******************************************',  # USDT合约地址
                'sort': 'desc',
                'page': 1,
                'offset': 100  # 限制返回数量
            }

            response = requests.get(url, params=params, timeout=15)
            time.sleep(self.request_delay * 2)  # 增加延迟避免频率限制

            if response.status_code == 200:
                data = response.json()
                transactions = []

                if data.get('status') == '1' and 'result' in data:
                    for tx in data['result']:
                        tx_timestamp = datetime.fromtimestamp(int(tx.get('timeStamp', 0)))

                        # 时间匹配（如果提供了时间戳）
                        if timestamp:
                            time_diff = abs((tx_timestamp - timestamp).total_seconds())
                            if time_diff > 3600:  # 超过1小时的交易忽略
                                continue

                        transactions.append({
                            'tx_hash': tx.get('hash'),
                            'from_address': tx.get('from'),
                            'to_address': tx.get('to'),
                            'amount': int(tx.get('value', 0)),
                            'timestamp': tx_timestamp,
                            'block': tx.get('blockNumber'),
                            'api_source': 'etherscan_free'
                        })

                return transactions
            elif response.status_code == 429:
                logger.warning(f"ERC20交易查询频率限制，跳过地址: {sender_address}")
                return []
            else:
                logger.warning(f"ERC20交易查询失败: {response.status_code}")
                return []

        except Exception as e:
            logger.error(f"ERC20交易查询异常 {sender_address}: {e}")
            return []
    
    def collect_xiaozeng_addresses(self) -> Dict:
        """
        收集小曾的真实收款地址
        
        Returns:
            收集结果字典
        """
        logger.info("开始收集小曾的收款地址...")
        
        # 1. 提取发送地址信息
        sender_info_list = self.extract_sender_addresses()
        
        # 2. 验证发送地址
        sender_addresses = [info['sender_address'] for info in sender_info_list]
        validation_results = self.validator.validate_address_list(sender_addresses)
        
        # 3. 查询有效地址的交易记录
        xiaozeng_receive_addresses = set()
        transaction_records = []
        
        for info in sender_info_list:
            sender_addr = info['sender_address']
            
            # 跳过无效地址
            if sender_addr in validation_results['invalid_addresses'] or sender_addr in validation_results['ignored_addresses']:
                continue
            
            logger.info(f"查询地址 {sender_addr} 的交易记录...")
            
            # 根据地址类型查询交易
            transactions = []
            
            if sender_addr in validation_results['trc20_addresses']:
                transactions = self.query_trc20_transaction(sender_addr, info['deposit_time'])
            elif sender_addr in validation_results['erc20_addresses']:
                transactions = self.query_erc20_transaction(sender_addr, info['deposit_time'])
            
            # 提取接收地址
            for tx in transactions:
                if tx['from_address'] == sender_addr:
                    receive_addr = tx['to_address']
                    if receive_addr:
                        xiaozeng_receive_addresses.add(receive_addr)
                        transaction_records.append({
                            'sender_address': sender_addr,
                            'receive_address': receive_addr,
                            'tx_hash': tx['tx_hash'],
                            'amount': tx['amount'],
                            'timestamp': tx['timestamp'],
                            'deposit_record_index': info['record_index']
                        })
        
        # 4. 验证收款地址
        receive_addresses_list = list(xiaozeng_receive_addresses)
        receive_validation = self.validator.validate_address_list(receive_addresses_list)
        
        result = {
            'sender_addresses': {
                'total': len(sender_addresses),
                'valid': validation_results['valid_addresses'],
                'invalid': validation_results['invalid_addresses'],
                'ignored': validation_results['ignored_addresses'],
                'validation_details': validation_results['validation_details']
            },
            'xiaozeng_receive_addresses': {
                'total': len(xiaozeng_receive_addresses),
                'addresses': receive_addresses_list,
                'trc20_addresses': receive_validation['trc20_addresses'],
                'erc20_addresses': receive_validation['erc20_addresses'],
                'validation_details': receive_validation['validation_details']
            },
            'transaction_records': transaction_records,
            'summary': {
                'total_sender_addresses': len(sender_addresses),
                'valid_sender_addresses': len(validation_results['valid_addresses']),
                'xiaozeng_receive_addresses_found': len(xiaozeng_receive_addresses),
                'transaction_records_found': len(transaction_records)
            }
        }
        
        logger.info(f"收集完成: 发现小曾收款地址 {len(xiaozeng_receive_addresses)} 个")
        return result

def main():
    """测试函数"""
    from data_reader import ExcelDataReader
    
    reader = ExcelDataReader("../..")
    collector = AddressCollector(reader)
    
    result = collector.collect_xiaozeng_addresses()
    
    print("=" * 60)
    print("小曾收款地址收集结果")
    print("=" * 60)
    print(f"发送地址总数: {result['summary']['total_sender_addresses']}")
    print(f"有效发送地址: {result['summary']['valid_sender_addresses']}")
    print(f"小曾收款地址: {result['summary']['xiaozeng_receive_addresses_found']}")
    print(f"交易记录: {result['summary']['transaction_records_found']}")

if __name__ == "__main__":
    main()
