#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地址验证模块
验证PDF扫描得来的区块链地址有效性，支持TRC20/ERC20地址格式验证和链上存在性验证
"""

import re
import base58
import requests
import time
from typing import Dict, List, Tuple, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AddressValidator:
    """区块链地址验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.tron_api_base = "https://apilist.tronscanapi.com/api"
        self.eth_api_base = "https://api.etherscan.io/api"
        self.request_delay = 0.2  # API请求间隔（秒）
        
        # 小江的UID列表（需要排除）
        self.xiajiang_uids = {'135343225', '1023681287', '451144524'}
        # 小曾的UID
        self.xiaozeng_uid = '96215380'
        
    def is_valid_trc20_address(self, address: str) -> bool:
        """
        验证TRC20地址格式
        
        Args:
            address: 地址字符串
            
        Returns:
            是否为有效的TRC20地址格式
        """
        try:
            # TRC20地址以T开头，长度34位
            if not address.startswith('T') or len(address) != 34:
                return False
            
            # Base58解码验证
            decoded = base58.b58decode(address)
            if len(decoded) != 25:
                return False
                
            # 验证校验和
            payload = decoded[:-4]
            checksum = decoded[-4:]
            
            import hashlib
            hash1 = hashlib.sha256(payload).digest()
            hash2 = hashlib.sha256(hash1).digest()
            
            return checksum == hash2[:4]
            
        except Exception as e:
            logger.debug(f"TRC20地址格式验证失败 {address}: {e}")
            return False
    
    def is_valid_erc20_address(self, address: str) -> bool:
        """
        验证ERC20地址格式
        
        Args:
            address: 地址字符串
            
        Returns:
            是否为有效的ERC20地址格式
        """
        try:
            # ERC20地址以0x开头，长度42位，包含十六进制字符
            if not address.startswith('0x') or len(address) != 42:
                return False
            
            # 验证是否为有效的十六进制
            int(address[2:], 16)
            return True
            
        except ValueError:
            logger.debug(f"ERC20地址格式验证失败 {address}: 非有效十六进制")
            return False
        except Exception as e:
            logger.debug(f"ERC20地址格式验证失败 {address}: {e}")
            return False
    
    def check_trc20_address_exists(self, address: str) -> Tuple[bool, Dict]:
        """
        检查TRC20地址在链上是否存在
        
        Args:
            address: TRC20地址
            
        Returns:
            (是否存在, 地址信息)
        """
        try:
            url = f"{self.tron_api_base}/account"
            params = {'address': address}
            
            response = requests.get(url, params=params, timeout=10)
            time.sleep(self.request_delay)
            
            if response.status_code == 200:
                data = response.json()
                
                # 检查是否有账户信息
                if 'data' in data and len(data['data']) > 0:
                    account_info = data['data'][0]
                    return True, {
                        'address': address,
                        'balance': account_info.get('balance', 0),
                        'create_time': account_info.get('date_created', 0),
                        'transaction_count': account_info.get('transactions', 0)
                    }
                else:
                    return False, {'error': '地址不存在或无交易记录'}
            else:
                return False, {'error': f'API请求失败: {response.status_code}'}
                
        except Exception as e:
            logger.error(f"TRC20地址存在性检查失败 {address}: {e}")
            return False, {'error': str(e)}
    
    def check_erc20_address_exists(self, address: str) -> Tuple[bool, Dict]:
        """
        检查ERC20地址在链上是否存在

        Args:
            address: ERC20地址

        Returns:
            (是否存在, 地址信息)
        """
        try:
            # 方案1: 使用免费的Etherscan API（无需API Key，但有频率限制）
            url = self.eth_api_base
            params = {
                'module': 'account',
                'action': 'balance',
                'address': address,
                'tag': 'latest'
            }

            response = requests.get(url, params=params, timeout=10)
            time.sleep(self.request_delay * 2)  # 增加延迟避免频率限制

            if response.status_code == 200:
                data = response.json()

                if data.get('status') == '1':
                    balance = int(data.get('result', '0'))
                    return True, {
                        'address': address,
                        'balance': balance,
                        'balance_eth': balance / 10**18,
                        'api_source': 'etherscan_free'
                    }
                elif 'rate limit' in data.get('message', '').lower():
                    # 如果遇到频率限制，尝试备用方案
                    logger.warning(f"Etherscan频率限制，尝试备用验证方案")
                    return self._check_erc20_address_alternative(address)
                else:
                    return False, {'error': data.get('message', '地址查询失败')}
            else:
                return False, {'error': f'API请求失败: {response.status_code}'}

        except Exception as e:
            logger.error(f"ERC20地址存在性检查失败 {address}: {e}")
            # 尝试备用方案
            return self._check_erc20_address_alternative(address)

    def _check_erc20_address_alternative(self, address: str) -> Tuple[bool, Dict]:
        """
        ERC20地址验证备用方案（仅格式验证）

        Args:
            address: ERC20地址

        Returns:
            (是否存在, 地址信息)
        """
        try:
            # 备用方案：仅进行格式验证，假设格式正确的地址存在
            if self.is_valid_erc20_address(address):
                return True, {
                    'address': address,
                    'balance': 'unknown',
                    'balance_eth': 'unknown',
                    'api_source': 'format_validation_only',
                    'note': '仅通过格式验证，未进行链上查询'
                }
            else:
                return False, {'error': '地址格式无效'}
        except Exception as e:
            return False, {'error': f'备用验证失败: {e}'}
    
    def should_ignore_address(self, address: str) -> bool:
        """
        判断是否应该忽略该地址
        
        Args:
            address: 地址字符串
            
        Returns:
            是否应该忽略
        """
        # 忽略小江的UID
        if address in self.xiajiang_uids:
            return True
        
        # 忽略纯数字地址（除了小曾的UID）
        if address.isdigit() and address != self.xiaozeng_uid:
            return True
        
        # 忽略明显错误的格式
        if len(address) < 10:
            return True
            
        return False
    
    def validate_address(self, address: str) -> Dict:
        """
        完整验证单个地址
        
        Args:
            address: 地址字符串
            
        Returns:
            验证结果字典
        """
        result = {
            'address': address,
            'should_ignore': False,
            'format_valid': False,
            'exists_on_chain': False,
            'address_type': 'unknown',
            'chain_info': {},
            'errors': []
        }
        
        # 检查是否应该忽略
        if self.should_ignore_address(address):
            result['should_ignore'] = True
            result['errors'].append('地址在忽略列表中')
            return result
        
        # 检查TRC20格式
        if self.is_valid_trc20_address(address):
            result['format_valid'] = True
            result['address_type'] = 'TRC20'
            
            # 检查链上存在性
            exists, info = self.check_trc20_address_exists(address)
            result['exists_on_chain'] = exists
            result['chain_info'] = info
            
            if not exists:
                result['errors'].append(f"TRC20地址在链上不存在: {info.get('error', '未知错误')}")
        
        # 检查ERC20格式
        elif self.is_valid_erc20_address(address):
            result['format_valid'] = True
            result['address_type'] = 'ERC20'
            
            # 检查链上存在性
            exists, info = self.check_erc20_address_exists(address)
            result['exists_on_chain'] = exists
            result['chain_info'] = info
            
            if not exists:
                result['errors'].append(f"ERC20地址在链上不存在: {info.get('error', '未知错误')}")
        
        else:
            result['errors'].append('地址格式不符合TRC20或ERC20标准')
        
        return result
    
    def validate_address_list(self, addresses: List[str]) -> Dict:
        """
        批量验证地址列表
        
        Args:
            addresses: 地址列表
            
        Returns:
            验证结果汇总
        """
        results = {
            'total_addresses': len(addresses),
            'ignored_addresses': [],
            'valid_addresses': [],
            'invalid_addresses': [],
            'trc20_addresses': [],
            'erc20_addresses': [],
            'validation_details': []
        }
        
        logger.info(f"开始验证 {len(addresses)} 个地址...")
        
        for i, address in enumerate(addresses, 1):
            logger.info(f"验证地址 {i}/{len(addresses)}: {address}")
            
            validation_result = self.validate_address(address)
            results['validation_details'].append(validation_result)
            
            if validation_result['should_ignore']:
                results['ignored_addresses'].append(address)
            elif validation_result['format_valid'] and validation_result['exists_on_chain']:
                results['valid_addresses'].append(address)
                
                if validation_result['address_type'] == 'TRC20':
                    results['trc20_addresses'].append(address)
                elif validation_result['address_type'] == 'ERC20':
                    results['erc20_addresses'].append(address)
            else:
                results['invalid_addresses'].append(address)
        
        logger.info(f"地址验证完成: 有效 {len(results['valid_addresses'])}, 无效 {len(results['invalid_addresses'])}, 忽略 {len(results['ignored_addresses'])}")
        
        return results

def main():
    """测试函数"""
    validator = AddressValidator()
    
    # 测试地址
    test_addresses = [
        "TRFkzRWd8tXynZHgen3H7qbpuRSnM2uWew",  # 有效TRC20
        "0x61f73c531c47d345ee642566d9d9fb38db287cdb",  # 有效ERC20
        "135343225",  # 小江UID（应忽略）
        "96215380",   # 小曾UID（应保留）
        "invalid_address",  # 无效地址
    ]
    
    results = validator.validate_address_list(test_addresses)
    
    print("=" * 60)
    print("地址验证测试结果")
    print("=" * 60)
    print(f"总地址数: {results['total_addresses']}")
    print(f"有效地址: {len(results['valid_addresses'])}")
    print(f"无效地址: {len(results['invalid_addresses'])}")
    print(f"忽略地址: {len(results['ignored_addresses'])}")
    print(f"TRC20地址: {len(results['trc20_addresses'])}")
    print(f"ERC20地址: {len(results['erc20_addresses'])}")

if __name__ == "__main__":
    main()
