#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MoneyTrace 主程序
实现0.简易分析模块和2.小曾收款地址获取模块
"""

import sys
import os
from pathlib import Path

# 添加模块路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "modules"))

from modules.data_reader import ExcelDataReader
from modules.simple_analyzer import SimpleAnalyzer
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    print("=" * 80)
    print("MoneyTrace 资金流向追踪分析系统")
    print("=" * 80)
    
    # 设置工作目录为项目根目录
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    print(f"工作目录: {os.getcwd()}")
    
    try:
        # 创建数据读取器
        print("\n1. 初始化数据读取器...")
        reader = ExcelDataReader(".")
        
        # 分析所有文件结构
        print("\n2. 分析Excel文件结构...")
        file_analysis = reader.analyze_all_files()
        
        # 显示文件分析结果
        print("\n文件分析结果:")
        print("-" * 60)
        for filename, info in file_analysis.items():
            if info.get('exists') and 'error' not in info:
                print(f"✓ {filename}: {info['rows']}行 x {info['columns']}列 - {info['description']}")
            else:
                print(f"✗ {filename}: {info.get('error', '未知错误')}")
        
        # 运行简易分析
        print("\n3. 执行0.简易分析模块...")
        print("-" * 60)
        analyzer = SimpleAnalyzer(reader)
        result = analyzer.run_simple_analysis()
        
        # 显示分析结果
        print("\n" + "=" * 80)
        print("0.简易分析模块 - 分析结果报告")
        print("=" * 80)
        
        print(f"\n📅 分析时间段:")
        print(f"   开始时间: {result['analysis_period']['start']}")
        print(f"   结束时间: {result['analysis_period']['end']}")
        print(f"   分析天数: {result['analysis_period']['duration_days']} 天")
        
        print(f"\n💰 小江币安充值情况:")
        print(f"   充值记录数: {result['xiajiang_deposits']['record_count']} 条")
        print(f"   充值USDT总额: {result['xiajiang_deposits']['total_usdt']:,.2f} USDT")
        
        print(f"\n🔄 小江P2P卖出情况:")
        print(f"   P2P卖出记录数: {result['xiajiang_p2p_sales']['record_count']} 条")
        print(f"   P2P卖出USDT总额: {result['xiajiang_p2p_sales']['total_usdt']:,.2f} USDT")
        
        print(f"\n🏦 法币收款匹配情况:")
        print(f"   匹配证据链数: {result['matched_fiat_receipts']['evidence_count']} 条")
        print(f"   可排除USDT金额: {result['matched_fiat_receipts']['matched_usdt']:,.2f} USDT")
        
        print(f"\n📊 最终计算结果:")
        print(f"   小江充值USDT总额: {result['final_calculation']['total_deposits_usdt']:,.2f} USDT")
        print(f"   可排除USDT金额: {result['final_calculation']['excluded_usdt_with_fiat_proof']:,.2f} USDT")
        print(f"   嫌疑流向小曾的USDT: {result['final_calculation']['suspected_flow_to_xiaozeng_usdt']:,.2f} USDT")
        print(f"   排除率: {result['final_calculation']['exclusion_rate']:.2f}%")
        
        # 保存分析结果
        print(f"\n4. 保存分析结果...")
        save_analysis_result(result)
        
        print(f"\n✅ 0.简易分析模块执行完成！")
        
        # 运行2.小曾收款地址获取模块
        print(f"\n5. 执行2.小曾收款地址获取模块...")
        print("-" * 60)
        xiaozeng_addresses = extract_xiaozeng_addresses(reader)
        
        print(f"\n📍 小曾收款地址分析结果:")
        print(f"   发现收款地址: {len(xiaozeng_addresses)} 个")
        for i, addr in enumerate(xiaozeng_addresses, 1):
            print(f"   {i}. {addr}")
        
        # 保存地址结果
        save_xiaozeng_addresses(xiaozeng_addresses)
        
        print(f"\n✅ 2.小曾收款地址获取模块执行完成！")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"\n❌ 程序执行失败: {e}")
        return 1
    
    return 0

def save_analysis_result(result):
    """保存分析结果到文件"""
    output_dir = Path("data/output")
    output_dir.mkdir(exist_ok=True)
    
    # 保存详细结果到文本文件
    with open(output_dir / "simple_analysis_report.txt", "w", encoding="utf-8") as f:
        f.write("MoneyTrace 简易分析报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"分析时间段: {result['analysis_period']['start']} 到 {result['analysis_period']['end']}\n")
        f.write(f"分析天数: {result['analysis_period']['duration_days']} 天\n\n")
        
        f.write("小江币安充值情况:\n")
        f.write(f"  充值记录数: {result['xiajiang_deposits']['record_count']} 条\n")
        f.write(f"  充值USDT总额: {result['xiajiang_deposits']['total_usdt']:,.2f} USDT\n\n")
        
        f.write("小江P2P卖出情况:\n")
        f.write(f"  P2P卖出记录数: {result['xiajiang_p2p_sales']['record_count']} 条\n")
        f.write(f"  P2P卖出USDT总额: {result['xiajiang_p2p_sales']['total_usdt']:,.2f} USDT\n\n")
        
        f.write("法币收款匹配情况:\n")
        f.write(f"  匹配证据链数: {result['matched_fiat_receipts']['evidence_count']} 条\n")
        f.write(f"  可排除USDT金额: {result['matched_fiat_receipts']['matched_usdt']:,.2f} USDT\n\n")
        
        f.write("最终计算结果:\n")
        f.write(f"  小江充值USDT总额: {result['final_calculation']['total_deposits_usdt']:,.2f} USDT\n")
        f.write(f"  可排除USDT金额: {result['final_calculation']['excluded_usdt_with_fiat_proof']:,.2f} USDT\n")
        f.write(f"  嫌疑流向小曾的USDT: {result['final_calculation']['suspected_flow_to_xiaozeng_usdt']:,.2f} USDT\n")
        f.write(f"  排除率: {result['final_calculation']['exclusion_rate']:.2f}%\n\n")
        
        # 保存证据链详情
        f.write("证据链详情:\n")
        f.write("-" * 30 + "\n")
        for i, evidence in enumerate(result['matched_fiat_receipts']['evidence_chain'], 1):
            f.write(f"证据链 {i}:\n")
            f.write(f"  P2P卖出USDT: {evidence['usdt_amount']:.2f}\n")
            f.write(f"  法币金额: {evidence['cny_amount']:.2f} CNY\n")
            f.write(f"  支付方式: {evidence['p2p_sale']['支付方式']}\n")
            f.write("\n")
    
    print(f"   分析报告已保存到: {output_dir / 'simple_analysis_report.txt'}")

def extract_xiaozeng_addresses(reader):
    """提取小曾的收款地址"""
    # 读取小曾币安充值记录
    xz_deposits = reader.read_xiaozeng_binance_deposits()

    # 提取发送地址
    addresses = set()
    for _, record in xz_deposits.iterrows():
        sender_addr = record['发送地址']
        if pd.notna(sender_addr):
            # 处理字符串和数字类型
            if isinstance(sender_addr, str):
                addr_str = sender_addr.strip()
                if addr_str:
                    addresses.add(addr_str)
            elif isinstance(sender_addr, (int, float)):
                # 如果是数字，转换为字符串
                addr_str = str(sender_addr).strip()
                if addr_str and addr_str != 'nan':
                    addresses.add(addr_str)

    return sorted(list(addresses))

def save_xiaozeng_addresses(addresses):
    """保存小曾收款地址到文件"""
    output_dir = Path("data/output")
    output_dir.mkdir(exist_ok=True)
    
    with open(output_dir / "xiaozeng_receive_addresses.txt", "w", encoding="utf-8") as f:
        f.write("小曾收款地址列表\n")
        f.write("=" * 30 + "\n\n")
        f.write(f"总计发现 {len(addresses)} 个收款地址:\n\n")
        
        for i, addr in enumerate(addresses, 1):
            f.write(f"{i}. {addr}\n")
    
    print(f"   小曾收款地址已保存到: {output_dir / 'xiaozeng_receive_addresses.txt'}")

if __name__ == "__main__":
    import pandas as pd
    exit_code = main()
    sys.exit(exit_code)
